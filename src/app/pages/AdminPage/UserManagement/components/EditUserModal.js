import React, { useEffect } from "react";
import { Modal, Form, Input, Select, Row, Col, Switch } from "antd";
import { useTranslation } from "react-i18next";

import AntButton from "@component/AntButton";
import { AntForm } from "@component/AntForm";
import { BUTTON } from "@constant";

const { Option } = Select;

const EditUserModal = ({ visible, onCancel, onFinish, userData, loading = false }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible && userData) {
      form.setFieldsValue({
        email: userData.email,
        fullName: userData.fullName,
        phone: userData.phone,
        gender: userData.gender,
        role: userData.role,
        type: userData.type,
        active: userData.active
      });
    }
  }, [visible, userData, form]);

  const handleSubmit = async (values) => {
    try {
      await onFinish(userData._id, values);
    } catch (error) {
      // Error handling is done in parent component
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={t("EDIT_USER")}
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={600}
      destroyOnClose
    >
      <AntForm
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        size="large"
      >
        <Row gutter={16}>
          <Col span={24}>
            <AntForm.Item
              name="email"
              label={t("EMAIL")}
            >
              <Input placeholder={t("ENTER_EMAIL")} disabled />
            </AntForm.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <AntForm.Item
              name="fullName"
              label={t("FULL_NAME")}
              rules={[
                { required: true, message: t("PLEASE_ENTER_FULL_NAME") },
                { min: 1, message: t("FULL_NAME_MIN_LENGTH") }
              ]}
            >
              <Input placeholder={t("ENTER_FULL_NAME")} />
            </AntForm.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <AntForm.Item
              name="phone"
              label={t("PHONE")}
            >
              <Input placeholder={t("ENTER_PHONE")} disabled />
            </AntForm.Item>
          </Col>
          <Col span={12}>
            <AntForm.Item
              name="gender"
              label={t("GENDER")}
            >
              <Select placeholder={t("SELECT_GENDER")} allowClear>
                <Option value="male">{t("MALE")}</Option>
                <Option value="female">{t("FEMALE")}</Option>
                <Option value="other">{t("OTHER")}</Option>
              </Select>
            </AntForm.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <AntForm.Item
              name="role"
              label={t("ROLE")}
            >
              <Select placeholder={t("SELECT_ROLE")}>
                <Option value="normal">{t("NORMAL")}</Option>
                <Option value="admin">{t("ADMIN")}</Option>
                <Option value="contributor">{t("CONTRIBUTOR")}</Option>
              </Select>
            </AntForm.Item>
          </Col>
          <Col span={12}>
            <AntForm.Item
              name="type"
              label={t("TYPE")}
            >
              <Select placeholder={t("SELECT_TYPE")}>
                <Option value="student">{t("STUDENT")}</Option>
                <Option value="teacher">{t("TEACHER")}</Option>
              </Select>
            </AntForm.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <AntForm.Item
              name="active"
              label={t("STATUS")}
              valuePropName="checked"
            >
              <Switch
                checkedChildren={t("ACTIVE")}
                unCheckedChildren={t("INACTIVE")}
              />
            </AntForm.Item>
          </Col>
        </Row>

        <div className="modal-footer">
          <AntButton
            type={BUTTON.GHOST_WHITE}
            size="large"
            onClick={handleCancel}
            disabled={loading}
          >
            {t("CANCEL")}
          </AntButton>
          <AntButton
            type={BUTTON.DEEP_NAVY}
            size="large"
            htmlType="submit"
            loading={loading}
          >
            {t("UPDATE")}
          </AntButton>
        </div>
      </AntForm>

      <style jsx>{`
        .modal-footer {
          display: flex;
          justify-content: flex-end;
          gap: 12px;
          margin-top: 24px;
          padding-top: 16px;
          border-top: 1px solid #f0f0f0;
        }
      `}</style>
    </Modal>
  );
};

export default EditUserModal;
