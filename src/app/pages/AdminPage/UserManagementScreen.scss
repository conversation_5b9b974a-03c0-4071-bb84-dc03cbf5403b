.user-management-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .user-management-info-card {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .user-management-info-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: 24px;

      .user-management-title {
        font-size: 28px;
        font-weight: 600;
        color: #1a1a1a;
        margin-bottom: 8px;
      }

      .user-management-description {
        font-size: 16px;
        color: #666;
        margin-bottom: 0;
        line-height: 1.5;
      }

      .btn-create-user {
        flex-shrink: 0;
        height: 48px;
        padding: 0 24px;
        font-weight: 500;
        border-radius: 6px;
      }
    }
  }

  .user-management-search-card {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .form-filter {
      .search-form-item {
        margin-bottom: 0;

        .ant-input,
        .ant-select-selector {
          border-radius: 6px;
          border: 1px solid #d9d9d9;
          
          &:hover {
            border-color: #40a9ff;
          }

          &:focus,
          &.ant-select-focused .ant-select-selector {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }
      }

      .search-buttons-col {
        display: flex;
        justify-content: flex-end;

        .admin-filter-buttons {
          display: flex;
          gap: 12px;

          .ant-btn {
            height: 40px;
            padding: 0 20px;
            font-weight: 500;
            border-radius: 6px;
          }
        }
      }
    }
  }

  .user-management-table-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .ant-table {
      .ant-table-thead > tr > th {
        background-color: #fafafa;
        font-weight: 600;
        color: #262626;
        border-bottom: 2px solid #f0f0f0;
      }

      .ant-table-tbody > tr {
        &:hover > td {
          background-color: #f5f5f5;
        }

        > td {
          border-bottom: 1px solid #f0f0f0;
          vertical-align: middle;
        }
      }
    }

    .user-name-cell {
      .user-name {
        font-weight: 500;
        color: #1a1a1a;
        margin-bottom: 2px;
      }

      .user-email {
        font-size: 12px;
        color: #666;
      }
    }

    .action-buttons {
      display: flex;
      gap: 8px;
      justify-content: center;

      .action-btn {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;

        &.action-btn-edit {
          background-color: #e6f7ff;
          color: #1890ff;

          &:hover {
            background-color: #bae7ff;
            transform: translateY(-1px);
          }
        }

        &.action-btn-delete {
          background-color: #fff2f0;
          color: #ff4d4f;

          &:hover {
            background-color: #ffccc7;
            transform: translateY(-1px);
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .user-management-container {
    padding: 16px;

    .user-management-info-card {
      .user-management-info-header {
        flex-direction: column;
        align-items: stretch;

        .btn-create-user {
          width: 100%;
          margin-top: 16px;
        }
      }
    }

    .user-management-search-card {
      .search-buttons-col {
        margin-top: 16px;

        .admin-filter-buttons {
          width: 100%;

          .ant-btn {
            flex: 1;
          }
        }
      }
    }
  }
}
