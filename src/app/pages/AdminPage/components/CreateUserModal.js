import React, { useState } from "react";
import { Modal, Form, Input, Select, Row, Col, Switch } from "antd";
import { useTranslation } from "react-i18next";

import AntButton from "@component/AntButton";
import { AntForm } from "@component/AntForm";
import { BUTTON } from "@constant";

const { Option } = Select;

const CreateUserModal = ({ visible, onCancel, onFinish, loading = false }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  const handleSubmit = async (values) => {
    try {
      await onFinish(values);
      form.resetFields();
    } catch (error) {
      // Error handling is done in parent component
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={t("CREATE_USER")}
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={600}
      destroyOnClose
    >
      <AntForm
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        size="large"
      >
        <Row gutter={16}>
          <Col span={24}>
            <AntForm.Item
              name="email"
              label={t("EMAIL")}
              rules={[
                { required: true, message: t("PLEASE_ENTER_EMAIL") },
                { type: "email", message: t("INVALID_EMAIL_FORMAT") }
              ]}
            >
              <Input placeholder={t("ENTER_EMAIL")} />
            </AntForm.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <AntForm.Item
              name="fullName"
              label={t("FULL_NAME")}
              rules={[
                { required: true, message: t("PLEASE_ENTER_FULL_NAME") },
                { min: 1, message: t("FULL_NAME_MIN_LENGTH") }
              ]}
            >
              <Input placeholder={t("ENTER_FULL_NAME")} />
            </AntForm.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <AntForm.Item
              name="phone"
              label={t("PHONE")}
            >
              <Input placeholder={t("ENTER_PHONE")} />
            </AntForm.Item>
          </Col>
          <Col span={12}>
            <AntForm.Item
              name="gender"
              label={t("GENDER")}
            >
              <Select placeholder={t("SELECT_GENDER")} allowClear>
                <Option value="male">{t("MALE")}</Option>
                <Option value="female">{t("FEMALE")}</Option>
                <Option value="other">{t("OTHER")}</Option>
              </Select>
            </AntForm.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <AntForm.Item
              name="role"
              label={t("ROLE")}
              initialValue="normal"
            >
              <Select placeholder={t("SELECT_ROLE")}>
                <Option value="normal">{t("NORMAL")}</Option>
                <Option value="admin">{t("ADMIN")}</Option>
                <Option value="contributor">{t("CONTRIBUTOR")}</Option>
              </Select>
            </AntForm.Item>
          </Col>
          <Col span={12}>
            <AntForm.Item
              name="type"
              label={t("TYPE")}
              initialValue="student"
            >
              <Select placeholder={t("SELECT_TYPE")}>
                <Option value="student">{t("STUDENT")}</Option>
                <Option value="teacher">{t("TEACHER")}</Option>
              </Select>
            </AntForm.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <AntForm.Item
              name="active"
              label={t("STATUS")}
              valuePropName="checked"
              initialValue={true}
            >
              <Switch 
                checkedChildren={t("ACTIVE")} 
                unCheckedChildren={t("INACTIVE")} 
              />
            </AntForm.Item>
          </Col>
          <Col span={12}>
            <AntForm.Item
              name="isSystemAdmin"
              label={t("SYSTEM_ADMIN")}
              valuePropName="checked"
              initialValue={false}
            >
              <Switch 
                checkedChildren={t("YES")} 
                unCheckedChildren={t("NO")} 
              />
            </AntForm.Item>
          </Col>
        </Row>

        <div className="modal-footer">
          <AntButton
            type={BUTTON.GHOST_WHITE}
            size="large"
            onClick={handleCancel}
            disabled={loading}
          >
            {t("CANCEL")}
          </AntButton>
          <AntButton
            type={BUTTON.DEEP_NAVY}
            size="large"
            htmlType="submit"
            loading={loading}
          >
            {t("CREATE")}
          </AntButton>
        </div>
      </AntForm>

      <style jsx>{`
        .modal-footer {
          display: flex;
          justify-content: flex-end;
          gap: 12px;
          margin-top: 24px;
          padding-top: 16px;
          border-top: 1px solid #f0f0f0;
        }
      `}</style>
    </Modal>
  );
};

export default CreateUserModal;
