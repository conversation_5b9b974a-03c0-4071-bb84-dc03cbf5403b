import { API } from "@api";
import { createBase, deleteBase, getAllBase, updateBase, getDetailBase, getAllPaginationBase } from "@services/Base";
import { cloneObj } from "@src/common/functionCommons";

/**
 * L<PERSON><PERSON> danh sách người dùng với phân trang
 * @param {Object} paging - Thông tin phân trang {page, limit}
 * @param {Object} query - Query filter
 * @param {Array} searchField - Các field để search
 * @returns {Promise} API response
 */
export const getAllUsers = (paging, query, searchField) => {
  const queryObj = cloneObj(query);
  queryObj.sort = queryObj.sort || "-createdAt";
  return getAllPaginationBase(API.USERS, paging, queryObj, searchField);
};

/**
 * Lấy tất cả người dùng không phân trang
 * @param {Object} query - Query filter
 * @param {Boolean} loading - Show loading
 * @param {Array} searchLike - Search fields
 * @returns {Promise} API response
 */
export const getAllUsersWithoutPaging = (query, loading = false, searchLike = []) => {
  return getAllBase(API.USERS + "/findAll", query, null, loading, searchLike);
};

/**
 * Lấy thông tin chi tiết một người dùng
 * @param {String} userId - ID của người dùng
 * @returns {Promise} API response
 */
export const getUserDetail = (userId) => {
  return getDetailBase(API.USER_ID, userId);
};

/**
 * Tạo người dùng mới
 * @param {Object} userData - Dữ liệu người dùng mới
 * @returns {Promise} API response
 */
export const createUser = (userData) => {
  return createBase(API.USERS, userData);
};

/**
 * Cập nhật thông tin người dùng
 * @param {String} userId - ID của người dùng
 * @param {Object} userData - Dữ liệu cập nhật
 * @returns {Promise} API response
 */
export const updateUserInfo = (userId, userData) => {
  const data = { ...userData, _id: userId };
  return updateBase(API.USER_ID, data);
};

/**
 * Xóa người dùng (soft delete)
 * @param {String} userId - ID của người dùng
 * @returns {Promise} API response
 */
export const deleteUser = (userId) => {
  return deleteBase(API.USER_ID, userId);
};

/**
 * Xóa người dùng khỏi tổ chức
 * @param {String} userId - ID của người dùng
 * @returns {Promise} API response
 */
export const removeUserFromOrganization = (userId) => {
  return deleteBase(API.REMOVE_USER_ORG, userId, false, true);
};
